<script lang="ts" setup>
import { ref } from 'vue'
import Accordion from 'primevue/accordion'
import AccordionPanel from 'primevue/accordionpanel'
import AccordionHeader from 'primevue/accordionheader'
import AccordionContent from 'primevue/accordioncontent'
import Badge from 'primevue/badge'
import { ClockIcon, ExclamationTriangleIcon, GlobeAltIcon, HashtagIcon, KeyIcon, PhoneIcon, PlayIcon, ShieldCheckIcon, SpeakerWaveIcon } from '@heroicons/vue/24/outline'

const phoneNumber = ref('(*************')
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header Section - Keeping your original design -->
    <div class="relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-emerald-600 via-emerald-500 to-teal-500 gradient-bg"></div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <div class="flex items-center justify-center mb-6">
            <PhoneIcon class="h-16 w-16 text-white animate-bounce-gentle" />
          </div>

          <h1 class="text-5xl md:text-6xl font-bold text-white mb-4 tracking-tight">Test Schedule Phone Line</h1>

          <p class="text-xl text-white max-w-3xl mx-auto">
            Call our automated phone system to get information about your upcoming test schedules. Follow these simple steps to verify your identity and receive your schedule
            information.
          </p>
        </div>
      </div>
    </div>

    <!-- Main Content - Redesigned -->
    <div class="py-20 px-4 sm:px-6 lg:px-8 relative">
      <!-- Background decoration -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-emerald-100 rounded-full opacity-20 bg-decoration"></div>
        <div class="absolute top-96 -left-40 w-60 h-60 bg-teal-100 rounded-full opacity-20 bg-decoration"></div>
      </div>

      <div class="max-w-6xl mx-auto relative">
        <!-- Quick Start Section - Redesigned -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <PlayIcon class="h-4 w-4" />
            Get Started
          </div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Three Simple Steps</h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">Access your test schedule information quickly and securely</p>
        </div>

        <!-- Steps Cards -->
        <div class="relative">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-12 md:gap-8 mb-20">
            <!-- Step 1 -->
            <div class="group flex flex-col h-full">
              <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 flex-1 flex flex-col step-card">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
                >
                  <PhoneIcon class="h-8 w-8 text-white" />
                </div>
                <div class="flex items-center gap-2 mb-3">
                  <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">1</span>
                  <h3 class="text-xl font-bold text-gray-900">Call the Number</h3>
                </div>
                <p class="text-gray-600 mb-4 flex-1">Dial {{ phoneNumber }} from your registered phone number</p>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-auto">
                  <p class="text-blue-800 text-sm font-medium">{{ phoneNumber }}</p>
                </div>
              </div>
            </div>

            <!-- Step 2 -->
            <div class="group flex flex-col h-full">
              <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 flex-1 flex flex-col step-card">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
                >
                  <GlobeAltIcon class="h-8 w-8 text-white" />
                </div>
                <div class="flex items-center gap-2 mb-3">
                  <span class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold">2</span>
                  <h3 class="text-xl font-bold text-gray-900">Select Language</h3>
                </div>
                <p class="text-gray-600 mb-4 flex-1">Choose your preferred language within 6 seconds</p>
                <div class="flex gap-2 mt-auto">
                  <div class="flex-1 bg-green-50 border border-green-200 rounded-lg p-2 text-center">
                    <Badge class="mb-1" severity="success" value="1" />
                    <p class="text-green-800 text-sm font-medium">English</p>
                  </div>
                  <div class="flex-1 bg-green-50 border border-green-200 rounded-lg p-2 text-center">
                    <Badge class="mb-1" severity="success" value="2" />
                    <p class="text-green-800 text-sm font-medium">Español</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 3 -->
            <div class="group flex flex-col h-full">
              <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 flex-1 flex flex-col step-card">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
                >
                  <KeyIcon class="h-8 w-8 text-white" />
                </div>
                <div class="flex items-center gap-2 mb-3">
                  <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-bold">3</span>
                  <h3 class="text-xl font-bold text-gray-900">Enter PIN</h3>
                </div>
                <p class="text-gray-600 mb-4 flex-1">Enter your 4-digit PIN followed by # within 10 seconds</p>
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-3 flex items-center gap-2 mt-auto">
                  <HashtagIcon class="h-4 w-4 text-purple-600" />
                  <p class="text-purple-800 text-sm font-medium">Don't forget the #</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Security Notice -->
        <div class="bg-gradient-to-r from-emerald-600 via-emerald-500 to-teal-500 rounded-2xl p-8 mb-20 text-white gradient-bg">
          <div class="flex items-start gap-4">
            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center flex-shrink-0">
              <ShieldCheckIcon class="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 class="text-xl font-bold mb-2">Important Security Notice</h3>
              <p class="text-white/90">
                You must call from the phone number registered in your patient account. This ensures your personal health information remains secure and protected.
              </p>
            </div>
          </div>
        </div>

        <!-- Key Information Grid -->
        <div class="mb-20">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">What You Need to Know</h2>
            <p class="text-lg text-gray-600">Important details for a smooth experience</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Time Limits -->
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div class="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center mb-4">
                <ClockIcon class="h-6 w-6 text-amber-600" />
              </div>
              <h4 class="font-bold text-gray-900 mb-2">Time Limits</h4>
              <div class="space-y-2 text-sm text-gray-600">
                <div class="flex justify-between">
                  <span>Language selection:</span>
                  <span class="font-medium">6 seconds</span>
                </div>
                <div class="flex justify-between">
                  <span>PIN entry:</span>
                  <span class="font-medium">10 seconds</span>
                </div>
              </div>
            </div>

            <!-- PIN Attempts -->
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mb-4">
                <ExclamationTriangleIcon class="h-6 w-6 text-red-600" />
              </div>
              <h4 class="font-bold text-gray-900 mb-2">Maximum Attempts</h4>
              <p class="text-sm text-gray-600">You have 3 attempts to enter your PIN correctly. After 3 failed attempts, the call will end automatically.</p>
            </div>

            <!-- Audio Quality -->
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-4">
                <SpeakerWaveIcon class="h-6 w-6 text-blue-600" />
              </div>
              <h4 class="font-bold text-gray-900 mb-2">Clear Audio</h4>
              <p class="text-sm text-gray-600">Ensure you're in a quiet environment with good phone reception for the best experience.</p>
            </div>
          </div>
        </div>

        <!-- FAQ Section -->
        <div class="mb-20">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p class="text-lg text-gray-600">Quick solutions to common issues</p>
          </div>

          <div class="bg-white rounded-2xl overflow-hidden accordion-container">
            <Accordion>
              <AccordionPanel class="accordion-panel" value="0">
                <AccordionHeader class="hover:bg-gray-50">
                  <div class="flex items-center gap-3 py-2">
                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                      <ExclamationTriangleIcon class="h-4 w-4 text-red-600" />
                    </div>
                    <span class="font-medium">I can't get through / Call doesn't connect</span>
                  </div>
                </AccordionHeader>
                <AccordionContent class="px-4 pb-6">
                  <div class="bg-gray-50 rounded-lg p-4 ml-11">
                    <p class="text-gray-700 mb-3">If you're having trouble connecting:</p>
                    <ul class="space-y-2 text-sm text-gray-600">
                      <li class="flex items-start gap-2">
                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span>Verify you're calling from your registered phone number</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span>Check your phone service connection</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span>Try calling again in a few minutes</span>
                      </li>
                    </ul>
                  </div>
                </AccordionContent>
              </AccordionPanel>

              <AccordionPanel class="accordion-panel" value="1">
                <AccordionHeader class="hover:bg-gray-50">
                  <div class="flex items-center gap-3 py-2">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                      <KeyIcon class="h-4 w-4 text-orange-600" />
                    </div>
                    <span class="font-medium">I forgot my PIN</span>
                  </div>
                </AccordionHeader>
                <AccordionContent class="px-4 pb-6">
                  <div class="bg-gray-50 rounded-lg p-4 ml-11">
                    <p class="text-gray-700 mb-3">If you've forgotten your 4-digit PIN:</p>
                    <ul class="space-y-2 text-sm text-gray-600">
                      <li class="flex items-start gap-2">
                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span>Contact your healthcare provider to reset your PIN</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span>Do not attempt to guess your PIN</span>
                      </li>
                    </ul>
                  </div>
                </AccordionContent>
              </AccordionPanel>

              <AccordionPanel class="accordion-panel" value="2">
                <AccordionHeader class="hover:bg-gray-50">
                  <div class="flex items-center gap-3 py-2">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                      <ClockIcon class="h-4 w-4 text-yellow-600" />
                    </div>
                    <span class="font-medium">The system hangs up too quickly</span>
                  </div>
                </AccordionHeader>
                <AccordionContent class="px-4 pb-6">
                  <div class="bg-gray-50 rounded-lg p-4 ml-11">
                    <p class="text-gray-700 mb-3">If the call ends too quickly:</p>
                    <ul class="space-y-2 text-sm text-gray-600">
                      <li class="flex items-start gap-2">
                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span>Be ready with your PIN before calling</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span>Ensure your phone's keypad tones are enabled</span>
                      </li>
                    </ul>
                  </div>
                </AccordionContent>
              </AccordionPanel>
            </Accordion>
          </div>
        </div>

        <!-- Contact Section -->
        <div class="text-center">
          <div class="bg-gradient-to-r from-emerald-600 via-emerald-500 to-teal-500 rounded-2xl p-12 text-white gradient-bg">
            <div class="w-20 h-20 bg-white/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <PhoneIcon class="h-10 w-10 text-white" />
            </div>
            <h2 class="text-2xl font-bold mb-4">Need Additional Help?</h2>
            <p class="text-white/90 mb-8 max-w-2xl mx-auto">
              If you continue to experience issues with the test schedule phone system, please contact your healthcare provider directly.
            </p>
            <div class="inline-flex items-center gap-4 bg-white/10 backdrop-blur rounded-xl p-6">
              <div class="text-left">
                <p class="text-white/80 text-sm">Test Schedule Phone Line</p>
                <p class="text-white font-mono text-2xl font-bold">{{ phoneNumber }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes bounce-gentle {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s infinite;
}
</style>
